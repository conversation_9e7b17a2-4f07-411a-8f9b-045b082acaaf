import { defineConfig, defineCollection } from "@content-collections/core";
import { rehypePrettyCodeConfig } from "./src/lib/rehype-pretty-code-config";
import rehypePrettyCode from "rehype-pretty-code";
import { z } from "zod";

const blog = defineCollection({
  name: "blog",
  directory: "src/content/blog",
  include: "**/*.mdx",
  schema: (z) => ({
    title: z.string(),
    description: z.string(),
    date: z.string(),
    author: z.string(),
    tags: z.array(z.string()),
    draft: z.boolean().default(false),
  }),
});

const docs = defineCollection({
  name: "docs",
  directory: "src/content/docs",
  include: "**/*.mdx",
  schema: (z) => ({
    title: z.string(),
    description: z.string(),
  }),
});

const changelog = defineCollection({
  name: "changelog",
  directory: "src/content/changelog",
  include: "**/*.mdx",
  schema: (z) => ({
    title: z.string(),
    description: z.string(),
    date: z.string(),
  }),
});

const legal = defineCollection({
  name: "legal",
  directory: "src/content/legal",
  include: "**/*.mdx",
  schema: (z) => ({
    title: z.string(),
  }),
});

export default defineConfig({
  collections: [blog, docs, changelog, legal],
  markdown: {
    rehypePlugins: [[rehypePrettyCode, rehypePrettyCodeConfig]],
  },
});
